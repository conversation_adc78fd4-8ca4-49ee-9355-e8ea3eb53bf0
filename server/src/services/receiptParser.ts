import type { ParsedReceiptData } from "shared";

export class ReceiptParser {
  /**
   * Parse receipt text and extract structured data
   */
  static parseReceiptText(pdfText: string): ParsedReceiptData {
    const lines = pdfText
      .split("\n")
      .map((line) => line.trim())
      .filter((line) => line.length > 0);

    const result: ParsedReceiptData = {
      store_name: "",
      purchase_date: "",
      purchase_total: 0,
      items: [],
      raw_pdf_text: pdfText,
    };

    try {
      // Extract store information (usually at the top)
      result.store_name = this.extractStoreName(lines);
      result.store_address = this.extractStoreAddress(lines);
      result.store_city = this.extractStoreCity(lines);
      result.store_state = this.extractStoreState(lines);
      result.store_zip_code = this.extractStoreZipCode(lines);
      result.store_phone_number = this.extractStorePhone(lines);

      // Extract purchase metadata
      result.purchase_date = this.extractPurchaseDate(lines);
      result.purchase_time = this.extractPurchaseTime(lines);
      result.ticket_number = this.extractTicketNumber(lines);

      // Extract financial information
      result.purchase_total = this.extractPurchaseTotal(lines);
      result.purchase_tax = this.extractPurchaseTax(lines);

      // Extract payment information
      result.credit_card_last_4 = this.extractCreditCardLast4(lines);
      result.payment_method = this.extractPaymentMethod(lines);

      // Extract line items (most complex part)
      result.items = this.extractLineItems(lines);

      return result;
    } catch (error) {
      console.error("Error parsing receipt:", error);
      // Return partial data even if parsing fails
      return result;
    }
  }

  /**
   * Extract store name (usually first non-empty line or line with common store indicators)
   */
  private static extractStoreName(lines: string[]): string {
    // Look for common store patterns
    const storePatterns = [
      /^(walmart|target|kroger|safeway|whole foods|costco|sam's club)/i,
      /store|market|grocery|supermarket/i,
    ];

    for (const line of lines.slice(0, 10)) {
      // Check first 10 lines
      for (const pattern of storePatterns) {
        if (pattern.test(line)) {
          return line;
        }
      }
    }

    // Fallback to first substantial line
    const fallbackLine = lines.find(
      (line) => line.length > 3 && !this.isNumericLine(line)
    );
    return fallbackLine || "Unknown Store";
  }

  /**
   * Extract store address
   */
  private static extractStoreAddress(lines: string[]): string | undefined {
    const addressPattern =
      /^\d+\s+[A-Za-z\s]+(?:st|street|ave|avenue|rd|road|blvd|boulevard|dr|drive|ln|lane|way|ct|court)/i;

    for (const line of lines.slice(0, 15)) {
      if (addressPattern.test(line)) {
        return line;
      }
    }
    return undefined;
  }

  /**
   * Extract store city
   */
  private static extractStoreCity(lines: string[]): string | undefined {
    // Look for city, state zip pattern
    const cityStateZipPattern =
      /^([A-Za-z\s]+),\s*([A-Z]{2})\s+(\d{5}(?:-\d{4})?)/;

    for (const line of lines.slice(0, 15)) {
      const match = line.match(cityStateZipPattern);
      if (match && match[1]) {
        return match[1].trim();
      }
    }
    return undefined;
  }

  /**
   * Extract store state
   */
  private static extractStoreState(lines: string[]): string | undefined {
    const cityStateZipPattern =
      /^([A-Za-z\s]+),\s*([A-Z]{2})\s+(\d{5}(?:-\d{4})?)/;

    for (const line of lines.slice(0, 15)) {
      const match = line.match(cityStateZipPattern);
      if (match) {
        return match[2];
      }
    }
    return undefined;
  }

  /**
   * Extract store zip code
   */
  private static extractStoreZipCode(lines: string[]): string | undefined {
    const cityStateZipPattern =
      /^([A-Za-z\s]+),\s*([A-Z]{2})\s+(\d{5}(?:-\d{4})?)/;

    for (const line of lines.slice(0, 15)) {
      const match = line.match(cityStateZipPattern);
      if (match) {
        return match[3];
      }
    }
    return undefined;
  }

  /**
   * Extract store phone number
   */
  private static extractStorePhone(lines: string[]): string | undefined {
    const phonePattern = /(\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4})/;

    for (const line of lines.slice(0, 15)) {
      const match = line.match(phonePattern);
      if (match) {
        return match[1];
      }
    }
    return undefined;
  }

  /**
   * Extract purchase date
   */
  private static extractPurchaseDate(lines: string[]): string {
    const datePatterns = [
      /(\d{1,2}\/\d{1,2}\/\d{4})/, // MM/DD/YYYY
      /(\d{4}-\d{2}-\d{2})/, // YYYY-MM-DD
      /(\d{1,2}-\d{1,2}-\d{4})/, // MM-DD-YYYY
    ];

    for (const line of lines) {
      for (const pattern of datePatterns) {
        const match = line.match(pattern);
        if (match && match[1]) {
          const dateStr = match[1];
          // Convert to ISO date format
          if (dateStr.includes("/")) {
            const parts = dateStr.split("/");
            if (parts.length === 3) {
              const [month, day, year] = parts;
              if (month && day && year) {
                return `${year}-${month.padStart(2, "0")}-${day.padStart(2, "0")}`;
              }
            }
          } else if (dateStr.includes("-") && dateStr.length === 10) {
            return dateStr; // Already in YYYY-MM-DD format
          } else if (dateStr.includes("-")) {
            const parts = dateStr.split("-");
            if (parts.length === 3) {
              const [month, day, year] = parts;
              if (month && day && year) {
                return `${year}-${month.padStart(2, "0")}-${day.padStart(2, "0")}`;
              }
            }
          }
        }
      }
    }

    // Fallback to current date
    const fallbackDate = new Date().toISOString().split("T")[0];
    return fallbackDate || "";
  }

  /**
   * Extract purchase time
   */
  private static extractPurchaseTime(lines: string[]): string | undefined {
    const timePattern = /(\d{1,2}:\d{2}(?::\d{2})?(?:\s*[AP]M)?)/i;

    for (const line of lines) {
      const match = line.match(timePattern);
      if (match && match[1]) {
        let timeStr = match[1];
        // Convert to 24-hour format if needed
        if (timeStr.toLowerCase().includes("pm") && !timeStr.startsWith("12")) {
          const timeParts = timeStr.replace(/[^\d:]/g, "").split(":");
          if (timeParts.length >= 2 && timeParts[0] && timeParts[1]) {
            const [hours, minutes] = timeParts;
            timeStr = `${parseInt(hours) + 12}:${minutes}`;
          }
        } else if (
          timeStr.toLowerCase().includes("am") &&
          timeStr.startsWith("12")
        ) {
          timeStr = timeStr.replace("12", "00").replace(/[^\d:]/g, "");
        } else {
          timeStr = timeStr.replace(/[^\d:]/g, "");
        }
        return timeStr;
      }
    }
    return undefined;
  }

  /**
   * Extract ticket number
   */
  private static extractTicketNumber(lines: string[]): string | undefined {
    const ticketPatterns = [
      /(?:ticket|receipt|trans|transaction)[\s#:]*(\w+)/i,
      /#(\d+)/,
    ];

    for (const line of lines) {
      for (const pattern of ticketPatterns) {
        const match = line.match(pattern);
        if (match) {
          return match[1];
        }
      }
    }
    return undefined;
  }

  /**
   * Extract purchase total
   */
  private static extractPurchaseTotal(lines: string[]): number {
    const totalPatterns = [
      /(?:total|amount due|balance)[\s:$]*(\d+\.?\d*)/i,
      /^total[\s:$]*(\d+\.?\d*)$/i,
    ];

    // Look from bottom up as total is usually at the end
    for (let i = lines.length - 1; i >= 0; i--) {
      const line = lines[i];
      if (line) {
        for (const pattern of totalPatterns) {
          const match = line.match(pattern);
          if (match && match[1]) {
            return parseFloat(match[1]);
          }
        }
      }
    }

    // Fallback: look for largest dollar amount
    let maxAmount = 0;
    for (const line of lines) {
      const amounts = line.match(/\$?(\d+\.\d{2})/g);
      if (amounts) {
        for (const amount of amounts) {
          const value = parseFloat(amount.replace("$", ""));
          if (value > maxAmount) {
            maxAmount = value;
          }
        }
      }
    }

    return maxAmount;
  }

  /**
   * Extract purchase tax
   */
  private static extractPurchaseTax(lines: string[]): number | undefined {
    const taxPattern = /(?:tax|sales tax)[\s:$]*(\d+\.?\d*)/i;

    for (const line of lines) {
      const match = line.match(taxPattern);
      if (match && match[1]) {
        return parseFloat(match[1]);
      }
    }
    return undefined;
  }

  /**
   * Extract credit card last 4 digits
   */
  private static extractCreditCardLast4(lines: string[]): string | undefined {
    const cardPattern = /(?:card|visa|mastercard|amex|discover).*?(\d{4})/i;

    for (const line of lines) {
      const match = line.match(cardPattern);
      if (match) {
        return match[1];
      }
    }
    return undefined;
  }

  /**
   * Extract payment method
   */
  private static extractPaymentMethod(lines: string[]): string | undefined {
    const paymentPatterns = [
      /(?:visa|mastercard|amex|american express|discover|debit|credit)/i,
      /(?:cash|check|ebt|food stamps)/i,
    ];

    for (const line of lines) {
      for (const pattern of paymentPatterns) {
        const match = line.match(pattern);
        if (match) {
          return match[0].toLowerCase();
        }
      }
    }
    return undefined;
  }

  /**
   * Extract line items (most complex parsing)
   */
  private static extractLineItems(lines: string[]): Array<{
    item_name: string;
    item_price: number;
    item_quantity: number;
    item_total_price: number;
    line_order: number;
  }> {
    const items: Array<{
      item_name: string;
      item_price: number;
      item_quantity: number;
      item_total_price: number;
      line_order: number;
    }> = [];

    let lineOrder = 1;

    for (const line of lines) {
      // Skip lines that are clearly not items
      if (this.isHeaderOrFooterLine(line)) {
        continue;
      }

      // Look for lines with item name and price
      const itemMatch = this.parseItemLine(line);
      if (itemMatch) {
        items.push({
          ...itemMatch,
          line_order: lineOrder++,
        });
      }
    }

    return items;
  }

  /**
   * Parse a single item line
   */
  private static parseItemLine(line: string): {
    item_name: string;
    item_price: number;
    item_quantity: number;
    item_total_price: number;
  } | null {
    // Pattern: item name followed by price
    // Examples: "Bananas 1.99", "Milk Gallon $3.49", "Bread 2 @ 2.50 5.00"

    const patterns = [
      // Pattern with quantity: "Item 2 @ 1.50 3.00"
      /^(.+?)\s+(\d+(?:\.\d+)?)\s*@\s*\$?(\d+\.\d{2})\s+\$?(\d+\.\d{2})$/,
      // Pattern with just price: "Item Name 1.99"
      /^(.+?)\s+\$?(\d+\.\d{2})$/,
      // Pattern with quantity and total: "Item 2 1.99"
      /^(.+?)\s+(\d+)\s+\$?(\d+\.\d{2})$/,
    ];

    for (const pattern of patterns) {
      const match = line.match(pattern);
      if (match) {
        if (
          match.length === 5 &&
          match[1] &&
          match[2] &&
          match[3] &&
          match[4]
        ) {
          // Pattern with quantity and unit price
          return {
            item_name: match[1].trim(),
            item_price: parseFloat(match[3]),
            item_quantity: parseFloat(match[2]),
            item_total_price: parseFloat(match[4]),
          };
        } else if (match.length === 3 && match[1] && match[2]) {
          // Pattern with just price (assume quantity 1)
          const price = parseFloat(match[2]);
          return {
            item_name: match[1].trim(),
            item_price: price,
            item_quantity: 1,
            item_total_price: price,
          };
        } else if (match.length === 4 && match[1] && match[2] && match[3]) {
          // Pattern with quantity and total price
          const quantity = parseFloat(match[2]);
          const totalPrice = parseFloat(match[3]);
          return {
            item_name: match[1].trim(),
            item_price: totalPrice / quantity,
            item_quantity: quantity,
            item_total_price: totalPrice,
          };
        }
      }
    }

    return null;
  }

  /**
   * Check if line is a header or footer (not an item)
   */
  private static isHeaderOrFooterLine(line: string): boolean {
    const skipPatterns = [
      /^(store|receipt|transaction|date|time|cashier|register)/i,
      /^(subtotal|tax|total|amount|balance|change|thank you)/i,
      /^(visa|mastercard|cash|credit|debit)/i,
      /^\d{1,2}\/\d{1,2}\/\d{4}/, // Date
      /^\d{1,2}:\d{2}/, // Time
      /^-+$/, // Separator lines
      /^=+$/, // Separator lines
    ];

    return skipPatterns.some((pattern) => pattern.test(line));
  }

  /**
   * Check if line is purely numeric
   */
  private static isNumericLine(line: string): boolean {
    return /^[\d\s\.\-\$]+$/.test(line);
  }
}
