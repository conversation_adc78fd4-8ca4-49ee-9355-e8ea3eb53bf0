import type { Context } from "hono";
import { getSupabase } from "../lib/middleware/supabase";
import type { Tables } from "@shared/types/database";

type Receipt = Tables<"receipts">;
type MasterItem = Tables<"master_items">;

export const supabaseService = {
  async insertReceipt(c: Context, receipt: Receipt) {
    const supabase = getSupabase(c);
    const { data, error } = await supabase
      .from("receipts")
      .insert(receipt)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to insert receipt: ${error.message}`);
    }

    return data;
  },
  async getReceipt(c: Context, receiptId: string) {
    const supabase = getSupabase(c);
    const { data, error } = await supabase
      .from("receipts")
      .select("*")
      .eq("id", receiptId)
      .single();

    if (error) {
      throw new Error(`Failed to fetch receipt: ${error.message}`);
    }

    return data;
  },
  async deleteReceipt(c: Context, receiptId: string) {
    const supabase = getSupabase(c);
    const { error } = await supabase
      .from("receipts")
      .delete()
      .eq("id", receiptId);

    if (error) {
      throw new Error(`Failed to delete receipt: ${error.message}`);
    }

    return;
  },
  async getAllReceiptsForUser(c: Context, userId: string) {
    const supabase = getSupabase(c);
    const { data, error } = await supabase
      .from("receipts")
      .select("*")
      .eq("user_id", userId)
      .order("purchase_date", { ascending: false });
    if (error) {
      throw new Error(`Failed to fetch receipts: ${error.message}`);
    }

    return data;
  },
  async getReceiptItems(c: Context, receiptId: string) {
    const supabase = getSupabase(c);
    const { data, error } = await supabase
      .from("receipt_items")
      .select("*")
      .eq("receipt_id", receiptId)
      .order("line_order", { ascending: true });
    if (error) {
      throw new Error(`Failed to fetch receipt items: ${error.message}`);
    }

    return data;
  },
  async getMasterItem(c: Context, masterItemId: string) {
    const supabase = getSupabase(c);
    const { data, error } = await supabase
      .from("master_items")
      .select("*")
      .eq("id", masterItemId)
      .single();

    if (error) {
      throw new Error(`Failed to fetch master item: ${error.message}`);
    }

    return data;
  },
  async insertMasterItem(c: Context, masterItem: MasterItem) {
    const supabase = getSupabase(c);
    const { data, error } = await supabase
      .from("master_items")
      .insert(masterItem)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to insert master item: ${error.message}`);
    }

    return data;
  },
};
