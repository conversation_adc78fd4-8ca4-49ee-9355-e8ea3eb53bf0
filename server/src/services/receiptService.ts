import { supabase } from "../lib/middleware/supabase";
import { ValidationService } from "../utils/validation";
import type {
  Receipt,
  ReceiptItem,
  CreateReceiptInput,
  CreateReceiptItemInput,
  ReceiptWithItems,
  MasterItem,
  ItemMatch,
} from "shared";

export class ReceiptService {
  /**
   * Create a new receipt with its items
   */
  async createReceiptWithItems(
    receiptData: CreateReceiptInput,
    items: Omit<CreateReceiptItemInput, "receipt_id">[]
  ): Promise<{ receipt: Receipt; items: ReceiptItem[] }> {
    try {
      // Validate receipt data
      const receiptErrors = ValidationService.validateReceiptData(receiptData);
      if (receiptErrors.length > 0) {
        throw new Error(
          `Receipt validation failed: ${receiptErrors.map((e) => `${e.field}: ${e.message}`).join(", ")}`
        );
      }

      // Validate receipt items (add temporary receipt_id for validation)
      const itemsWithTempId = items.map((item, index) => ({
        ...item,
        receipt_id: "temp-id",
        line_order: item.line_order || index + 1,
      }));

      const itemErrors =
        ValidationService.validateReceiptItems(itemsWithTempId);
      if (itemErrors.length > 0) {
        throw new Error(
          `Receipt items validation failed: ${itemErrors.map((e) => `${e.field}: ${e.message}`).join(", ")}`
        );
      }
      // Start a transaction by creating the receipt first
      const { data: receipt, error: receiptError } = await supabase
        .from("receipts")
        .insert({
          user_id: receiptData.user_id || null, // For now, allow null user_id
          store_name: receiptData.store_name,
          store_address: receiptData.store_address || null,
          store_city: receiptData.store_city || null,
          store_state: receiptData.store_state || null,
          store_zip_code: receiptData.store_zip_code || null,
          store_phone_number: receiptData.store_phone_number || null,
          purchase_date: receiptData.purchase_date,
          purchase_time: receiptData.purchase_time || null,
          ticket_number: receiptData.ticket_number || null,
          purchase_total: receiptData.purchase_total,
          purchase_tax: receiptData.purchase_tax || null,
          credit_card_last_4: receiptData.credit_card_last_4 || null,
          payment_method: receiptData.payment_method || null,
          raw_pdf_text: receiptData.raw_pdf_text || null,
        })
        .select()
        .single();

      if (receiptError) {
        throw new Error(`Failed to create receipt: ${receiptError.message}`);
      }

      if (!receipt) {
        throw new Error("Receipt was not created");
      }

      // Create receipt items
      const itemsToInsert = items.map((item) => ({
        receipt_id: receipt.id,
        item_name: item.item_name,
        item_price: item.item_price,
        item_quantity: item.item_quantity,
        item_total_price: item.item_total_price,
        line_order: item.line_order,
      }));

      const { data: createdItems, error: itemsError } = await supabase
        .from("receipt_items")
        .insert(itemsToInsert)
        .select();

      if (itemsError) {
        // If items creation fails, we should ideally rollback the receipt
        // For now, we'll throw an error
        throw new Error(
          `Failed to create receipt items: ${itemsError.message}`
        );
      }

      if (!createdItems) {
        throw new Error("Receipt items were not created");
      }

      // Create master items and matches for MVP (1:1 relationship)
      await this.createMasterItemsForReceiptItems(createdItems);

      return {
        receipt: receipt as Receipt,
        items: createdItems as ReceiptItem[],
      };
    } catch (error) {
      throw new Error(
        `Receipt creation failed: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  /**
   * Create master items and matches for receipt items (MVP approach)
   */
  private async createMasterItemsForReceiptItems(
    receiptItems: ReceiptItem[]
  ): Promise<void> {
    for (const item of receiptItems) {
      try {
        // Check if master item already exists
        const { data: existingMasterItem } = await supabase
          .from("master_items")
          .select("id")
          .eq("normalized_name", item.item_name.toLowerCase().trim())
          .single();

        let masterItemId: string;

        if (existingMasterItem) {
          masterItemId = existingMasterItem.id;
        } else {
          // Create new master item
          const { data: newMasterItem, error: masterItemError } = await supabase
            .from("master_items")
            .insert({
              normalized_name: item.item_name.toLowerCase().trim(),
              category: null, // Will be added in future phases
            })
            .select("id")
            .single();

          if (masterItemError || !newMasterItem) {
            console.error(
              `Failed to create master item for ${item.item_name}:`,
              masterItemError
            );
            continue; // Skip this item match if master item creation fails
          }

          masterItemId = newMasterItem.id;
        }

        // Create item match
        const { error: matchError } = await supabase
          .from("item_matches")
          .insert({
            receipt_item_id: item.id,
            master_item_id: masterItemId,
            confidence_score: 1.0, // Perfect match for MVP
            match_method: "exact",
          });

        if (matchError) {
          console.error(
            `Failed to create item match for ${item.item_name}:`,
            matchError
          );
        }
      } catch (error) {
        console.error(
          `Error processing master item for ${item.item_name}:`,
          error
        );
      }
    }
  }

  /**
   * Get a receipt by ID with its items
   */
  async getReceiptWithItems(
    receiptId: string,
    userId?: string
  ): Promise<ReceiptWithItems | null> {
    try {
      let query = supabase
        .from("receipts")
        .select(
          `
          *,
          receipt_items (*)
        `
        )
        .eq("id", receiptId);

      // Add user filter if provided
      if (userId) {
        query = query.eq("user_id", userId);
      }

      const { data, error } = await query.single();

      if (error) {
        if (error.code === "PGRST116") {
          return null; // Receipt not found
        }
        throw new Error(`Failed to fetch receipt: ${error.message}`);
      }

      return data as ReceiptWithItems;
    } catch (error) {
      throw new Error(
        `Failed to get receipt: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  /**
   * Get all receipts for a user
   */
  async getUserReceipts(
    userId?: string,
    limit = 50,
    offset = 0
  ): Promise<Receipt[]> {
    try {
      let query = supabase
        .from("receipts")
        .select("*")
        .order("purchase_date", { ascending: false })
        .range(offset, offset + limit - 1);

      // Add user filter if provided
      if (userId) {
        query = query.eq("user_id", userId);
      }

      const { data, error } = await query;

      if (error) {
        throw new Error(`Failed to fetch receipts: ${error.message}`);
      }

      return (data || []) as Receipt[];
    } catch (error) {
      throw new Error(
        `Failed to get user receipts: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  /**
   * Delete a receipt and all its items
   */
  async deleteReceipt(receiptId: string, userId?: string): Promise<boolean> {
    try {
      let query = supabase.from("receipts").delete().eq("id", receiptId);

      // Add user filter if provided
      if (userId) {
        query = query.eq("user_id", userId);
      }

      const { error } = await query;

      if (error) {
        throw new Error(`Failed to delete receipt: ${error.message}`);
      }

      return true;
    } catch (error) {
      throw new Error(
        `Failed to delete receipt: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  /**
   * Get receipt items for a specific receipt
   */
  async getReceiptItems(receiptId: string): Promise<ReceiptItem[]> {
    try {
      const { data, error } = await supabase
        .from("receipt_items")
        .select("*")
        .eq("receipt_id", receiptId)
        .order("line_order", { ascending: true });

      if (error) {
        throw new Error(`Failed to fetch receipt items: ${error.message}`);
      }

      return (data || []) as ReceiptItem[];
    } catch (error) {
      throw new Error(
        `Failed to get receipt items: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }
}
