import { supabase } from "../lib/middleware/supabase";
import type {
  MonthlySpendingData,
  ItemPriceHistory,
  StoreSpendingData,
  ExpensiveItemData,
  AnalyticsQueryParams,
  ItemPriceQueryParams,
} from "shared";

export class AnalyticsService {
  /**
   * Get monthly spending data
   */
  async getMonthlySpending(
    params: AnalyticsQueryParams
  ): Promise<MonthlySpendingData[]> {
    try {
      let query = `
        SELECT 
          TO_CHAR(purchase_date, 'YYYY-MM') as month,
          SUM(purchase_total) as total_spent,
          COUNT(*) as receipt_count,
          AVG(purchase_total) as average_per_receipt
        FROM receipts 
        WHERE 1=1
      `;

      const queryParams: any[] = [];
      let paramIndex = 1;

      // Add filters
      if (params.user_id) {
        query += ` AND user_id = $${paramIndex}`;
        queryParams.push(params.user_id);
        paramIndex++;
      }

      if (params.start_date) {
        query += ` AND purchase_date >= $${paramIndex}`;
        queryParams.push(params.start_date);
        paramIndex++;
      }

      if (params.end_date) {
        query += ` AND purchase_date <= $${paramIndex}`;
        queryParams.push(params.end_date);
        paramIndex++;
      }

      if (params.store_name) {
        query += ` AND store_name ILIKE $${paramIndex}`;
        queryParams.push(`%${params.store_name}%`);
        paramIndex++;
      }

      query += ` GROUP BY TO_CHAR(purchase_date, 'YYYY-MM') ORDER BY month DESC`;

      const { data, error } = await supabase.rpc("execute_sql", {
        sql: query,
        params: queryParams,
      });

      if (error) {
        throw new Error(`Failed to fetch monthly spending: ${error.message}`);
      }

      return (data || []).map((row: any) => ({
        month: row.month,
        total_spent: parseFloat(row.total_spent),
        receipt_count: parseInt(row.receipt_count),
        average_per_receipt: parseFloat(row.average_per_receipt),
      }));
    } catch (error) {
      // Fallback to Supabase query builder if RPC fails
      return this.getMonthlySpendingFallback(params);
    }
  }

  /**
   * Fallback method for monthly spending using Supabase query builder
   */
  private async getMonthlySpendingFallback(
    params: AnalyticsQueryParams
  ): Promise<MonthlySpendingData[]> {
    try {
      let query = supabase
        .from("receipts")
        .select("purchase_date, purchase_total");

      // Add filters
      if (params.user_id) {
        query = query.eq("user_id", params.user_id);
      }

      if (params.start_date) {
        query = query.gte("purchase_date", params.start_date);
      }

      if (params.end_date) {
        query = query.lte("purchase_date", params.end_date);
      }

      if (params.store_name) {
        query = query.ilike("store_name", `%${params.store_name}%`);
      }

      const { data, error } = await query;

      if (error) {
        throw new Error(
          `Failed to fetch receipts for monthly spending: ${error.message}`
        );
      }

      // Group by month in JavaScript
      const monthlyData: { [key: string]: { total: number; count: number } } =
        {};

      (data || []).forEach((receipt) => {
        const month = receipt.purchase_date.substring(0, 7); // YYYY-MM
        if (!monthlyData[month]) {
          monthlyData[month] = { total: 0, count: 0 };
        }
        monthlyData[month].total += receipt.purchase_total;
        monthlyData[month].count += 1;
      });

      return Object.entries(monthlyData)
        .map(([month, data]) => ({
          month,
          total_spent: data.total,
          receipt_count: data.count,
          average_per_receipt: data.total / data.count,
        }))
        .sort((a, b) => b.month.localeCompare(a.month));
    } catch (error) {
      throw new Error(
        `Failed to get monthly spending: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  /**
   * Get item price history
   */
  async getItemPriceHistory(
    params: ItemPriceQueryParams
  ): Promise<ItemPriceHistory | null> {
    try {
      let query = supabase
        .from("receipt_items")
        .select(
          `
          item_name,
          item_price,
          receipt_id,
          receipts!inner (
            purchase_date,
            store_name,
            user_id
          )
        `
        )
        .ilike("item_name", `%${params.item_name}%`);

      // Add filters
      if (params.user_id) {
        query = query.eq("receipts.user_id", params.user_id);
      }

      if (params.start_date) {
        query = query.gte("receipts.purchase_date", params.start_date);
      }

      if (params.end_date) {
        query = query.lte("receipts.purchase_date", params.end_date);
      }

      if (params.store_name) {
        query = query.ilike("receipts.store_name", `%${params.store_name}%`);
      }

      const { data, error } = await query.order("receipts.purchase_date", {
        ascending: true,
      });

      if (error) {
        throw new Error(`Failed to fetch item price history: ${error.message}`);
      }

      if (!data || data.length === 0) {
        return null;
      }

      const pricePoints = data.map((item: any) => ({
        date: item.receipts.purchase_date,
        price: item.item_price,
        store_name: item.receipts.store_name,
        receipt_id: item.receipt_id,
      }));

      const prices = pricePoints.map((p) => p.price);
      const averagePrice =
        prices.reduce((sum, price) => sum + price, 0) / prices.length;
      const minPrice = Math.min(...prices);
      const maxPrice = Math.max(...prices);

      // Simple trend calculation
      let priceTrend: "increasing" | "decreasing" | "stable" = "stable";
      if (pricePoints.length >= 2) {
        const firstPrice = pricePoints[0]?.price;
        const lastPrice = pricePoints[pricePoints.length - 1]?.price;

        if (firstPrice && lastPrice) {
          const percentChange = ((lastPrice - firstPrice) / firstPrice) * 100;

          if (percentChange > 5) {
            priceTrend = "increasing";
          } else if (percentChange < -5) {
            priceTrend = "decreasing";
          }
        }
      }

      return {
        item_name: params.item_name,
        price_points: pricePoints,
        average_price: averagePrice,
        min_price: minPrice,
        max_price: maxPrice,
        price_trend: priceTrend,
      };
    } catch (error) {
      throw new Error(
        `Failed to get item price history: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  /**
   * Get store spending data
   */
  async getStoreSpending(
    params: AnalyticsQueryParams
  ): Promise<StoreSpendingData[]> {
    try {
      let query = supabase
        .from("receipts")
        .select("store_name, purchase_total, purchase_date");

      // Add filters
      if (params.user_id) {
        query = query.eq("user_id", params.user_id);
      }

      if (params.start_date) {
        query = query.gte("purchase_date", params.start_date);
      }

      if (params.end_date) {
        query = query.lte("purchase_date", params.end_date);
      }

      if (params.store_name) {
        query = query.ilike("store_name", `%${params.store_name}%`);
      }

      const { data, error } = await query;

      if (error) {
        throw new Error(
          `Failed to fetch store spending data: ${error.message}`
        );
      }

      // Group by store in JavaScript
      const storeData: {
        [key: string]: { total: number; count: number; dates: string[] };
      } = {};

      (data || []).forEach((receipt) => {
        const storeName = receipt.store_name;
        if (!storeData[storeName]) {
          storeData[storeName] = { total: 0, count: 0, dates: [] };
        }
        storeData[storeName].total += receipt.purchase_total;
        storeData[storeName].count += 1;
        storeData[storeName].dates.push(receipt.purchase_date);
      });

      return Object.entries(storeData)
        .map(([storeName, data]) => {
          const sortedDates = data.dates.sort();
          return {
            store_name: storeName,
            total_spent: data.total,
            receipt_count: data.count,
            average_per_receipt: data.total / data.count,
            first_visit: sortedDates[0] || "",
            last_visit: sortedDates[sortedDates.length - 1] || "",
          };
        })
        .sort((a, b) => b.total_spent - a.total_spent);
    } catch (error) {
      throw new Error(
        `Failed to get store spending: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  /**
   * Get most expensive items
   */
  async getExpensiveItems(
    params: AnalyticsQueryParams,
    limit = 20
  ): Promise<ExpensiveItemData[]> {
    try {
      let query = supabase.from("receipt_items").select(`
          item_name,
          item_price,
          item_total_price,
          receipts!inner (
            store_name,
            user_id,
            purchase_date
          )
        `);

      // Add filters through receipts
      if (params.user_id) {
        query = query.eq("receipts.user_id", params.user_id);
      }

      if (params.start_date) {
        query = query.gte("receipts.purchase_date", params.start_date);
      }

      if (params.end_date) {
        query = query.lte("receipts.purchase_date", params.end_date);
      }

      if (params.store_name) {
        query = query.ilike("receipts.store_name", `%${params.store_name}%`);
      }

      const { data, error } = await query;

      if (error) {
        throw new Error(`Failed to fetch expensive items: ${error.message}`);
      }

      // Group by item name in JavaScript
      const itemData: {
        [key: string]: {
          prices: number[];
          totalSpent: number;
          count: number;
          stores: Set<string>;
        };
      } = {};

      (data || []).forEach((item: any) => {
        const itemName = item.item_name;
        if (!itemData[itemName]) {
          itemData[itemName] = {
            prices: [],
            totalSpent: 0,
            count: 0,
            stores: new Set(),
          };
        }
        itemData[itemName].prices.push(item.item_price);
        itemData[itemName].totalSpent += item.item_total_price;
        itemData[itemName].count += 1;
        itemData[itemName].stores.add(item.receipts.store_name);
      });

      return Object.entries(itemData)
        .map(([itemName, data]) => ({
          item_name: itemName,
          highest_price: Math.max(...data.prices),
          average_price:
            data.prices.reduce((sum, price) => sum + price, 0) /
            data.prices.length,
          purchase_count: data.count,
          total_spent: data.totalSpent,
          stores: Array.from(data.stores),
        }))
        .sort((a, b) => b.highest_price - a.highest_price)
        .slice(0, limit);
    } catch (error) {
      throw new Error(
        `Failed to get expensive items: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }
}
